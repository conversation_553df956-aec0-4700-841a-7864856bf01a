"""
Product Repository - Database operations for product management.
Handles all database interactions for product operations.
"""

from managers.logger_manager import logger
import mysql.connector
from typing import Dict, Optional, List, Any
from datetime import datetime
from uuid import uuid4

from os import getenv
from dotenv import load_dotenv
import random

from infrastructure.external_apis.jetveo_client import product_change_status_async

load_dotenv()

class ProductRepository:
    """Repository for product database operations"""
    
    def __init__(self):
        self.logger = logger
    
    def _get_db_connection(self):
        """Get database connection"""
        return mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )
    

    def get_product_sections(self) -> List[Dict[str, Any]]:
        """Get all product sections with availability status based on sale_reservations"""
        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)

        try:
            # Get all sections with mode = 'sale'
            cursor.execute("""
                SELECT section_id, identification_name, tempered, blocked, service,
                       mode, type, size_width, size_depth, size_height, size_category
                FROM box_sections
                WHERE visible = 1 AND mode = 'sale'
                ORDER BY section_id ASC
            """)
            sections = cursor.fetchall()

            # Check availability for each section based on sale_reservations
            for section in sections:
                section_id = section['section_id']
                section['is_available'] = self._check_product_section_availability(cursor, section_id)

            return sections

        except mysql.connector.Error as err:
            logger.error(f"Database error getting product sections: {err}")
            return []
        finally:
            cursor.close()
            conn.close()

    def _check_product_section_availability(self, cursor, section_id: int) -> bool:
        """Check if a product section is available (no active reservations in sale_reservations)"""
        try:
            cursor.execute("""
                SELECT id FROM sale_reservations
                WHERE section_id = %s AND status != 0
                LIMIT 1
            """, (section_id,))

            active_reservation = cursor.fetchone()
            return active_reservation is None

        except mysql.connector.Error as err:
            logger.error(f"Database error checking product section availability: {err}")
            return False

    async def handle_section_open(self, session_id: str, section_id: int, endpoint_type: str):
        """Handle section open for product endpoints"""
        # session_id is kept for interface compatibility but not used in current implementation
        match endpoint_type:
            case "product/pickup":
                # Deactivate product reservation using new universal function
                result = await self.edit_reservation(
                    section_id=section_id,
                    status_to_set=0
                )
                return result is not None
            case _:
                pass  # No action needed for other endpoints


    def create_reservation(
        self,
        section_id: int,
        price: float,
        ean: Optional[str] = None,   # if type is ean, this is required
        type: str = "custom",       # custom / ean
        name: Optional[str] = None,
        reserved: Optional[bool] = 0,
        reservation_pin: Optional[str] = None,
        description: Optional[str] = None,
        age_control_required: Optional[bool] = False,
        cover_image: Optional[str] = None,   # path to cover image of the product (if any)
        max_days: Optional[int] = None,       # max days till it reservation expires (not implemented in current schema)
        paid_status: Optional[bool] = 0,      # if order was paid
        ) -> Optional[Dict[str, Any]]:


        """
        Function to create new reservation record in sale_reservations table.

        Args:
            section_id: Section ID where product will be placed
            price: Product price
            ean: EAN code (required if type is 'ean')
            type: Product type ('custom' or 'ean')
            name: Product name
            reserved: Whether product is reserved (0 or 1)
            reservation_pin: Reservation PIN (if reserved)
            description: Product description
            age_control_required: Whether age control is required
            cover_image: Path to cover image
            max_days: Max days until reservation expires (not implemented in current schema)
            paid_status: Payment status (0 or 1)

        Returns:
            Dict with created reservation data if successful, None otherwise
        """

        # Note: max_days parameter is not implemented in current database schema
        # but kept for future compatibility

        # Validate required parameters
        if type == "ean" and not ean:
            self.logger.error("EAN is required when type is 'ean'")
            return None

        if reserved and not reservation_pin:
            # Generate PIN if reserved but no PIN provided
            from .pin_generator import generate_pin
            reservation_pin = generate_pin()
            if not reservation_pin:
                self.logger.error("Failed to generate unique PIN for reservation")
                return None

        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)

        try:
            # Generate new UUID
            new_uuid = str(uuid4())

            # Insert new reservation
            cursor.execute("""
                INSERT INTO sale_reservations
                (uuid, section_id, ean, status, type, name, description, price,
                 reserved, reservation_pin, age_control_required, cover_image,
                 paid_status, created_at, last_update)
                VALUES (%s, %s, %s, 1, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                new_uuid,
                str(section_id),
                ean,
                type,
                name,
                description,
                float(price),
                int(reserved) if reserved else 0,
                reservation_pin,
                int(age_control_required) if age_control_required else 0,
                cover_image,
                int(paid_status) if paid_status else 0,
                datetime.now(),
                datetime.now()
            ))

            conn.commit()

            # Send product change status to Jetveo
            product_change_status_async(
                reservation_uuid=new_uuid,
                section_id=section_id,
                price=float(price),
                ean=ean,
                reserved=bool(reserved),
                reservation_pin=reservation_pin,
                action=1,
                status=1
            )

            # Get the inserted record
            cursor.execute("""
                SELECT * FROM sale_reservations
                WHERE uuid = %s
            """, (new_uuid,))

            result = cursor.fetchone()
            self.logger.info(f"Successfully created reservation {new_uuid} in section {section_id}")
            return result

        except mysql.connector.Error as err:
            conn.rollback()
            self.logger.error(f"Database error creating reservation: {err}")
            return None
        finally:
            cursor.close()
            conn.close()



    def find_reservations(
        self,
        # Search parameters
        section_id: Optional[int] = None,
        reservation_pin: Optional[str] = None,
        product_uuid: Optional[str] = None,
        status: Optional[int] = None,
        reserved: Optional[int] = None,
        type: Optional[str] = None,
        ean: Optional[str] = None,
        paid_status: Optional[str] = None,
        limit: Optional[int] = None,
        order_by: str = "created_at DESC"
        ) -> List[Dict[str, Any]]:

        """
        Function to find reservations based on search parameters.

        Args:
            section_id: Section ID to search in
            reservation_pin: Reservation PIN to search by
            product_uuid: Product UUID to search by
            status: Status to filter by (0=inactive, 1=active)
            reserved: Reserved status to filter by (0=not reserved, 1=reserved)
            type: Product type to filter by ('custom', 'ean')
            ean: EAN code to search by
            paid_status: Payment status to filter by
            limit: Maximum number of results to return
            order_by: Order by clause (default: "created_at DESC")

        Returns:
            List of reservation records matching the criteria
        """

        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)

        try:
            # Build WHERE clause dynamically
            where_conditions = []
            params = []

            if section_id is not None:
                where_conditions.append("section_id = %s")
                params.append(str(section_id))

            if reservation_pin is not None:
                where_conditions.append("reservation_pin = %s")
                params.append(reservation_pin)

            if product_uuid is not None:
                where_conditions.append("uuid = %s")
                params.append(product_uuid)

            if status is not None:
                where_conditions.append("status = %s")
                params.append(status)

            if reserved is not None:
                where_conditions.append("reserved = %s")
                params.append(reserved)

            if type is not None:
                where_conditions.append("type = %s")
                params.append(type)

            if ean is not None:
                where_conditions.append("ean = %s")
                params.append(ean)

            if paid_status is not None:
                where_conditions.append("paid_status = %s")
                params.append(paid_status)

            # Build the query
            query = "SELECT * FROM sale_reservations"
            if where_conditions:
                query += " WHERE " + " AND ".join(where_conditions)
            query += f" ORDER BY {order_by}"
            if limit is not None:
                query += f" LIMIT {limit}"

            cursor.execute(query, params)
            results = cursor.fetchall()

            self.logger.info(f"Found {len(results)} reservations matching criteria")
            return results

        except mysql.connector.Error as err:
            self.logger.error(f"Database error finding reservations: {err}")
            return []
        finally:
            cursor.close()
            conn.close()



    async def edit_reservation(
        self,
        # Search parameters
        section_id: Optional[int] = None,
        reservation_pin: Optional[str] = None,
        product_uuid: Optional[str] = None,
        product_id: Optional[int] = None,
        status_to_find: Optional[int] = 1,

        # Update parameters
        status_to_set: Optional[int] = None,
        reserved: Optional[int] = None,
        reservation_pin_new: Optional[str] = None,
        price: Optional[float] = None,
        name: Optional[str] = None,
        description: Optional[str] = None,
        ean: Optional[str] = None,
        type: Optional[str] = None,
        age_control_required: Optional[bool] = None,
        cover_image: Optional[str] = None,
        paid_status: Optional[str] = None,
        action: Optional[int] = None
        ) -> Optional[Dict[str, Any]]:

        """
        Function to edit reservation based on search parameters.

        Args:
            # Search parameters (at least one must be provided)
            section_id: Section ID to find reservation
            reservation_pin: Reservation PIN to find reservation
            product_uuid: Product UUID to find reservation
            product_id: Product ID to find reservation
            status_to_find: Status to search for (default: 1=active)

            # Update parameters (at least one must be provided)
            status_to_set: New status (0=inactive, 1=active)
            reserved: New reserved status (0=not reserved, 1=reserved)
            reservation_pin_new: New reservation PIN
            price: New price
            name: New name
            description: New description
            ean: New EAN code
            type: New type
            age_control_required: New age control requirement
            cover_image: New cover image path
            paid_status: New payment status
            action: Action code for Jetveo (0=purchased, 1=inserted, 2=removed, 3=no change)

        Returns:
            Updated reservation record if successful, None otherwise
        """

        # Validate that at least one search parameter is provided
        search_params = [section_id, reservation_pin, product_uuid, product_id]
        if not any(param is not None for param in search_params):
            self.logger.error("At least one search parameter must be provided")
            return None

        # Validate that at least one update parameter is provided
        update_params = [status_to_set, reserved, reservation_pin_new, price, name, description,
                        ean, type, age_control_required, cover_image, paid_status]
        if not any(param is not None for param in update_params):
            self.logger.error("At least one update parameter must be provided")
            return None

        conn = self._get_db_connection()
        cursor = conn.cursor(dictionary=True)

        try:
            # First, find the reservation to get current data
            where_conditions = []
            search_query_params = []

            if product_id is not None:
                where_conditions.append("id = %s")
                search_query_params.append(product_id)
            elif product_uuid is not None:
                where_conditions.append("uuid = %s")
                search_query_params.append(product_uuid)
            elif reservation_pin is not None:
                where_conditions.append("reservation_pin = %s")
                search_query_params.append(reservation_pin)
            elif section_id is not None:
                where_conditions.append("section_id = %s")
                search_query_params.append(str(section_id))

            # Add status_to_find condition if specified
            if status_to_find is not None:
                where_conditions.append("status = %s")
                search_query_params.append(status_to_find)

            # Get current reservation data
            find_query = f"SELECT * FROM sale_reservations WHERE {' AND '.join(where_conditions)} LIMIT 1"
            cursor.execute(find_query, search_query_params)
            current_reservation = cursor.fetchone()

            if not current_reservation:
                self.logger.warning("No reservation found matching search criteria")
                return None

            # Build UPDATE query dynamically
            update_fields = []
            update_query_params = []

            if status_to_set is not None:
                update_fields.append("status = %s")
                update_query_params.append(status_to_set)

            if reserved is not None:
                update_fields.append("reserved = %s")
                update_query_params.append(int(reserved))

            if reservation_pin_new is not None:
                update_fields.append("reservation_pin = %s")
                update_query_params.append(reservation_pin_new)

            if price is not None:
                update_fields.append("price = %s")
                update_query_params.append(float(price))

            if name is not None:
                update_fields.append("name = %s")
                update_query_params.append(name)

            if description is not None:
                update_fields.append("description = %s")
                update_query_params.append(description)

            if ean is not None:
                update_fields.append("ean = %s")
                update_query_params.append(ean)

            if type is not None:
                update_fields.append("type = %s")
                update_query_params.append(type)

            if age_control_required is not None:
                update_fields.append("age_control_required = %s")
                update_query_params.append(int(age_control_required))

            if cover_image is not None:
                update_fields.append("cover_image = %s")
                update_query_params.append(cover_image)

            if paid_status is not None:
                update_fields.append("paid_status = %s")
                update_query_params.append(paid_status)

            # Always update last_update timestamp
            update_fields.append("last_update = %s")
            update_query_params.append(datetime.now())

            # Add search parameters to update query params
            update_query_params.extend(search_query_params)

            # Execute update
            update_query = f"UPDATE sale_reservations SET {', '.join(update_fields)} WHERE {' AND '.join(where_conditions)}"
            cursor.execute(update_query, update_query_params)

            conn.commit()

            if cursor.rowcount > 0:
                # Get updated record - use a query without status condition since status may have changed
                # Build a new query using only the primary search criteria (not status_to_find)
                fetch_where_conditions = []
                fetch_query_params = []

                if product_id is not None:
                    fetch_where_conditions.append("id = %s")
                    fetch_query_params.append(product_id)
                elif product_uuid is not None:
                    fetch_where_conditions.append("uuid = %s")
                    fetch_query_params.append(product_uuid)
                elif reservation_pin is not None:
                    fetch_where_conditions.append("reservation_pin = %s")
                    fetch_query_params.append(reservation_pin)
                elif section_id is not None:
                    fetch_where_conditions.append("section_id = %s")
                    fetch_query_params.append(str(section_id))

                fetch_query = f"SELECT * FROM sale_reservations WHERE {' AND '.join(fetch_where_conditions)} LIMIT 1"
                cursor.execute(fetch_query, fetch_query_params)
                updated_reservation = cursor.fetchone()

                if updated_reservation:
                    # Send product change status to Jetveo for ALL edited reservations
                    try:
                        product_change_status_async(
                            reservation_uuid=updated_reservation['uuid'],
                            section_id=updated_reservation['section_id'],
                            price=updated_reservation.get('price'),
                            ean=updated_reservation.get('ean'),
                            reserved=bool(updated_reservation.get('reserved', 0)),
                            reservation_pin=updated_reservation.get('reservation_pin'),
                            action=action if action else 3,  # "action": , // 1 - inserted / 2 - removed / 3 - nothing changes / 0 - purchased
                            status=updated_reservation.get('status')
                        )
                        self.logger.info(f"Sent product_change_status for reservation {updated_reservation['uuid']}")
                    except Exception as e:
                        self.logger.error(f"Error sending product_change_status for reservation {updated_reservation['uuid']}: {e}")

                    self.logger.info(f"Successfully updated reservation {updated_reservation['uuid']}")
                    return updated_reservation
                else:
                    self.logger.warning("Updated reservation not found after successful update")
                    return None
            else:
                self.logger.warning("No rows updated in edit_reservation")
                return None

        except mysql.connector.Error as err:
            conn.rollback()
            self.logger.error(f"Database error editing reservation: {err}")
            return None
        finally:
            cursor.close()
            conn.close()






# Global repository instance
product_repository = ProductRepository()
