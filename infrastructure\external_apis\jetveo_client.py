"""
Jetveo API Client - External API integration for product data.
Handles communication with Jetveo API for product information, PIN validation, and heartbeat.

Features:
1. Product Data: Fetch product information by EAN, search products, get categories
2. PIN Validation: Validate operator PINs against Jetveo server
3. Heartbeat Service: Automatic "I'm alive" messages sent periodically

PIN Check Integration:
- The check_pin() method is ready to use for PIN validation
- To switch from MockResponse to real API in box/router.py:
  1. Uncomment: response_data = await jetveo_client.check_pin(pin, SERIAL_NUMBER or "unknown")
  2. Remove the MockResponse lines
  3. Ensure EXTERNAL_API_BASE_URL and EXTERNAL_API_TOKEN are properly configured

Heartbeat Service:
- Automatically started when the application starts (see main.py lifespan)
- Only runs if EXTERNAL_API_ENABLE environment variable is set to true
- Sends POST /api/imalive every 9 minutes by default
- Configurable via JETVEO_HEARTBEAT_INTERVAL_MINUTES environment variable
- Uses SERIAL_NUMBER from environment for device identification
"""

import aiohttp
import asyncio
import mysql.connector
import json
import os
from typing import Optional, Dict, Any, Tuple, List
from os import getenv
from dotenv import load_dotenv
import threading
from datetime import datetime, timedelta
import time
from sale.models import ExternalProduct, ExternalProductResponse
from managers.logger_manager import logger

load_dotenv()

class JetveoClient:
    """Client for Jetveo external API"""
    
    def __init__(self):
        self.logger = logger
        self.base_url = getenv("EXTERNAL_API_BASE_URL", "https://4a8fa4e1-8948-4b4c-9baa-4839d158ad96.eu.jetveo.io")
        self.partner_code = getenv("EXTERNAL_API_PARTNER_CODE", "drmax")
        self.api_token = getenv("EXTERNAL_API_TOKEN", "Gj1lojJdMIrgC13psFsWwveas8PYLdUC")
        self.timeout = int(getenv("EXTERNAL_API_TIMEOUT", "10"))
        self.serial_number = getenv("SERIAL_NUMBER", "unknown")
        self.external_api_enabled = getenv("EXTERNAL_API_ENABLE", "false").lower() in ("true", "1", "yes", "on")
        # Handle temperature sensor pins configuration
        temp_pins = getenv("TEMPERATURE_SENSOR_PINS", "1,2,3,4,5,6,7,8,9")
        self.temperature_sensor_pins = temp_pins.split(",") if temp_pins else []

        # Handle comet IP addresses configuration
        comet_ips = getenv("COMET_IP_ADDRESSES", "*************,*************")
        self.comet_ip_addresses = comet_ips.split(",") if comet_ips else []
        self._heartbeat_task = None
        self.echo_loop_running = False
        self.order_server_response_required = getenv("ORDER_SERVER_RESPONSE_REQUIRED", "false").lower() in ("true", "1", "yes", "on")
        self.heartbeat_duration = int(getenv("JETVEO_HEARTBEAT_INTERVAL_MINUTES", "9")) * 60  # Convert to seconds
        self.queue_duration = int(getenv("EXTERNAL_API_REQUEST_QUEUE_DURATION", "300"))  # 5 minutes default
        self.temperature_sensor_enabled = getenv("TEMPERATURE_SENSOR_ENABLED", "false").lower() in ("true", "1", "yes", "on")
        self.temperature_comet_enabled = getenv("TEMPERATURE_COMET_ENABLED", "false").lower() in ("true", "1", "yes", "on")
        self.last_echo_duration = 0
        self.last_queue_duration = 0
        


    def _get_db_connection(self):
        """Get database connection for server request queue"""
        return mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )
    
    async def fetch_product_by_ean(self, ean: str) -> Optional[ExternalProduct]:
        """
        Fetch product information from external API by EAN code.
        Only fetches if external API is enabled.

        Args:
            ean: EAN code to search for

        Returns:
            ExternalProduct object if found, None otherwise
        """
        if not self.external_api_enabled:
            self.logger.info("External API is disabled, skipping product fetch")
            return None

        payload = {
            "offset": 0,
            "limit": 100,  # Search in larger batch to increase chance of finding EAN
            "partner-code": self.partner_code
        }

        success, data = await self.send_to_server("/api/product", payload, "product_search", method="get")

        if not success or not data:
            self.logger.error("Failed to fetch products from external API")
            return None

        try:
            # Parse response using Pydantic model
            external_response = ExternalProductResponse(**data)

            if external_response.success:
                # Search for product with matching EAN
                for product in external_response.items:
                    if product.ean == ean:
                        self.logger.info(f"Found product for EAN {ean}: {product.text.cs.name}")
                        return product

                self.logger.warning(f"Product with EAN {ean} not found in external API")
                return None
            else:
                self.logger.error(f"External API returned unsuccessful response: {external_response.message}")
                return None

        except Exception as e:
            self.logger.error(f"Error parsing external API response: {e}")
            return None


    
    async def search_products(self, query: str, limit: int = 100) -> Optional[ExternalProductResponse]:
        """
        Search products in external API.
        Only searches if external API is enabled.

        Args:
            query: Search query
            limit: Maximum number of results

        Returns:
            ExternalProductResponse object or None if failed/disabled
        """
        if not self.external_api_enabled:
            self.logger.info("External API is disabled, skipping product search")
            return None

        payload = {
            "offset": 0,
            "limit": limit,
            "partner-code": self.partner_code,
            "q": query
        }

        success, data = await self.send_to_server("/api/product", payload, "product_search", method="get")

        if not success or not data:
            self.logger.error(f"Failed to search products in external API for query: {query}")
            return None

        try:
            # Parse response using Pydantic model
            external_response = ExternalProductResponse(**data)
            self.logger.info(f"Found {len(external_response.items)} products for query: {query}")
            return external_response

        except Exception as e:
            self.logger.error(f"Error parsing external API search response: {e}")
            return None
    
    
    async def get_product_categories(self) -> Optional[Dict[str, Any]]:
        """
        Get product categories from external API.
        Only fetches if external API is enabled.

        Returns:
            Categories data or None if failed/disabled
        """
        if not self.external_api_enabled:
            self.logger.info("External API is disabled, skipping categories fetch")
            return None

        payload = {
            "partner-code": self.partner_code
        }

        success, data = await self.send_to_server("/api/categories", payload, "categories", method="get", save_to_queues=False)
        return data


    async def check_operator_pin(self, pin: str) -> Optional[Dict[str, Any]]:
        """
        Check PIN validity with external API.
        Only checks if external API is enabled.

        Args:
            pin: PIN code to validate
            serial_number: Device serial number

        Returns:
            Dict with PIN check result or None if failed/disabled
            Expected response format:
            {
                "serial_number": "xxxx",
                "status": "allow" | "deny",
                "operator_id": int,
                "name": "Operator Name",
                "type": "1" | "2" | etc.
            }
        """
        if not self.external_api_enabled:
            self.logger.info("External API is disabled, skipping PIN check")
            return {
                "serial_number": "xxxx",
                "status": "deny",
                "operator_id": 1,
                "name": "Tung Sahur",
                "type": "1"
            }

        payload = {
            "SerialNumber": self.serial_number,
            "ScannedPin": pin
        }

        success, data = await self.send_to_server("/api/operator-login", payload, "operator_login", save_to_queues=False)
        return data
    

    async def send_heartbeat(self) -> bool:
        """
        Send "I'm alive" heartbeat to Jetveo server.
        Only sends if external API is enabled.

        Returns:
            True if heartbeat sent successfully, False otherwise
        """
        if not self.external_api_enabled:
            self.logger.info("External API is disabled, skipping heartbeat")
            return False

        payload = {
            "SerialNumber": self.serial_number
        }

        success, data = await self.send_to_server("/api/imalive", payload, "heartbeat")
        return success

    async def echo_loop(self):
        """
        Internal echo loop that runs periodically.
        Also retries failed requests from the queue.
        """
        self.logger.info(f"Starting echo loop")

        while self.echo_loop_running:

            try:
                await asyncio.sleep(3)
                current_time = time.time()

                # send hearbeat
                if self.last_echo_duration + self.heartbeat_duration < current_time:
                    self.last_echo_duration = current_time
                    await self.send_heartbeat()

                    # send electronic sensors data
                    from hardware.temperatures import temperature_controller
                    if self.temperature_sensor_enabled:
                        electronic_temps = await temperature_controller.get_electronic_sensor_temp_all()
                        await self.send_temperature_electronic(electronic_temps)

                    # send comet sensores data
                    if self.temperature_comet_enabled:
                        comet_temps = await temperature_controller.get_comet_sensors_temp_all()
                        await self.send_temperature_comet(comet_temps)

                # retry failed requests
                if self.last_queue_duration + self.queue_duration < current_time:
                    self.last_queue_duration = current_time
                    await self.retry_failed_requests()

            except ImportError as e:
                self.logger.warning(f"Could not import temperature controller: {e}")
            except Exception as e:
                self.logger.error(f"Echo loop error in echo loop: {e}")

            except asyncio.CancelledError:
                self.logger.info("Echo loop cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in echo loop: {e}")
                # Wait a bit before retrying
                await asyncio.sleep(60)



    def start_echo_loop(self):
        """
        Start the periodic heartbeat in the background.
        Only starts if external API is enabled.
        """
        if not self.external_api_enabled:
            self.logger.info("External API is disabled, echo loop will not start")
            return

        if self.echo_loop_running:
            self.logger.warning("Echo loop is already running")
            return

        self.echo_loop_running = True
        # Create new event loop for the echo loop if we're not in an async context
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # We're in an async context, create task
                self._heartbeat_task = loop.create_task(self.echo_loop())
            else:
                # Start in a separate thread
                def run_echo_loop():
                    try:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        loop.run_until_complete(self.echo_loop())
                    except Exception as e:
                        self.logger.error(f"Echo loop error: {e}")
                    finally:
                        self.echo_loop_running = False

                heartbeat_thread = threading.Thread(target=run_echo_loop, daemon=True)
                heartbeat_thread.start()

        except RuntimeError:
            # No event loop, start in a separate thread
            def run_echo_loop():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(self.echo_loop())
                except Exception as e:
                    self.logger.error(f"Echo loop error: {e}")
                finally:
                    self.echo_loop_running = False

            heartbeat_thread = threading.Thread(target=run_echo_loop, daemon=True)
            heartbeat_thread.start()

        self.logger.info("Echo loop started")

    def stop_echo_loop(self):
        """
        Stop the periodic echo loop.
        """
        if not self.echo_loop_running:
            return

        self.echo_loop_running = False

        if self._heartbeat_task and not self._heartbeat_task.done():
            self._heartbeat_task.cancel()

        self.logger.info("Echo loop stopped")

    async def check_employment_send(self, phone_number: str) -> Optional[Dict[str, Any]]:
        """
        Check if phone number is valid for employment send operation.

        Args:
            phone_number: Employee's phone number

        Returns:
            Dict with validation result or None if failed
            Expected response format:
            {
                "phone_number": "123456789",
                "valid": true,
                "section_id": null/1
            }
        """
        if not self.external_api_enabled:
            self.logger.info("External API is disabled, using mock response for employment send check")
            # Mock responses for testing
            if phone_number == "123456789":
                return {
                    "phone_number": phone_number,
                    "valid": True,
                    "section_id": 5  # Pre-reserved section
                }
            elif phone_number == "987654321":
                return {
                    "phone_number": phone_number,
                    "valid": True,
                    "section_id": None  # No pre-reserved section
                }
            else:
                return {
                    "phone_number": phone_number,
                    "valid": False,
                    "section_id": None
                }

        payload = {
            "phone_number": phone_number
        }

        success, data = await self.send_to_server("/api/employment/send", payload, "employment_send")
        return data

    async def check_employment_deliver(self, phone_number: str) -> Optional[Dict[str, Any]]:
        """
        Check if phone number is valid for employment deliver operation.

        Args:
            phone_number: Employee's phone number

        Returns:
            Dict with validation result or None if failed
            Expected response format:
            {
                "phone_number": "123456789",
                "valid": true,
                "section_ids": null/[1,2,3]
            }
        """
        if not self.external_api_enabled:
            self.logger.info("External API is disabled, using mock response for employment deliver check")
            # Mock responses for testing
            if phone_number == "123456789":
                return {
                    "phone_number": phone_number,
                    "valid": True,
                    "section_ids": [1, 2, 3]  # Pre-reserved sections for delivery
                }
            elif phone_number == "555666777":
                return {
                    "phone_number": phone_number,
                    "valid": True,
                    "section_ids": None  # No pre-reserved sections
                }
            else:
                return {
                    "phone_number": phone_number,
                    "valid": False,
                    "section_ids": None
                }

        payload = {
            "phone_number": phone_number
        }

        success, data = await self.send_to_server("/api/employment/deliver", payload, "employment_deliver")
        return data



    async def check_order_number_deliver(self, order_number: str) -> Tuple[bool, bool]:
        """
        Check if order number is valid for customer deliver operation.

        Args:
            order_number: Order number

        Returns:
            First bool if response from server was succesfull
            Second bool is True if the order number is valid, False otherwise
        """
        if not self.external_api_enabled:
            self.logger.info("External API is disabled, using mock response for order number deliver check")
            return True, True

        payload = {
            "order_number": order_number
        }
        
        success, data = await self.send_to_server("/api/order/deliver", payload, "order_deliver", save_to_queues=not self.order_server_response_required)
        return success, data.get("valid", False)
    



    async def check_employment_reclaim(self, reclamation_pin: str) -> Optional[Dict[str, Any]]:
        """
        Check if reclamation PIN is valid for employment reclaim operation.

        Args:
            reclamation_pin: Reclamation PIN to validate

        Returns:
            Dict with validation result or None if failed
            Expected response format:
            {
                "phone_number": "123456789",
                "valid": true,
                "section_id": null/1
            }
        """
        if not self.external_api_enabled:
            self.logger.info("External API is disabled, using mock response for employment reclaim check")
            # Mock responses for testing
            if reclamation_pin == "RECLAIM123":
                return {
                    "phone_number": "123456789",
                    "valid": True,
                    "section_id": 7  # Pre-reserved section for reclaim
                }
            elif reclamation_pin == "RECLAIM456":
                return {
                    "phone_number": "987654321",
                    "valid": True,
                    "section_id": None  # No pre-reserved section
                }
            else:
                return {
                    "phone_number": None,
                    "valid": False,
                    "section_id": None
                }

        success, data = await self.send_to_server("/api/employment/reclaim", {"reservation_pin": reclamation_pin}, "employment_reclaim")
        return data
    

    async def get_layout(self, serial_number: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Get layout data from external API or return mock data.
        If external API is disabled, returns mock response from JSON file.

        Args:
            serial_number: Serial number to use for the request. If None, uses self.serial_number

        Returns:
            Dict with layout data if successful, None otherwise
        """
        if not self.external_api_enabled:
            self.logger.info("External API is disabled, returning mock layout data")
            return self._get_mock_layout_response()

        payload = {
            "SerialNumber": serial_number or self.serial_number
        }

        success, data = await self.send_to_server("/api/layout", payload, "layout", method="get")
        return data

    def _get_mock_layout_response(self) -> Optional[Dict[str, Any]]:
        """
        Load and return mock layout response from JSON file.

        Returns:
            Dict with mock layout data if file exists and is valid, None otherwise
        """
        try:
            # Get the directory where this file is located
            current_dir = os.path.dirname(os.path.abspath(__file__))
            mock_file_path = os.path.join(current_dir, "mock_layout_response.json")

            if not os.path.exists(mock_file_path):
                self.logger.error(f"Mock layout response file not found: {mock_file_path}")
                return None

            with open(mock_file_path, 'r', encoding='utf-8') as f:
                mock_data = json.load(f)

            self.logger.info("Mock layout data loaded successfully")
            return mock_data

        except json.JSONDecodeError as e:
            self.logger.error(f"Error parsing mock layout JSON file: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error loading mock layout response: {e}")
            return None

    async def send_to_journal(
        self,
        serial_number: str,
        timestamp: str,
        entered_pin: Optional[str] = None,
        event_result: Optional[str] = None,
        event_type: Optional[str] = None,
        message: Optional[str] = None,
        operator_id: Optional[str] = None,
        order_number: Optional[str] = None,
        section_id: Optional[str] = None,
        session_id: Optional[str] = None,
        tempered_unlock: Optional[str] = None,
        type: Optional[str] = None
    ) -> bool:
        """
        Send journal data to Jetveo API /api/journal endpoint.
        Only sends if external API is enabled. If request fails, saves to server_request_queue.

        Args:
            serial_number: Device serial number (required)
            timestamp: Event timestamp in ISO format (required)
            entered_pin: PIN that was entered
            event_result: Result of the event
            event_type: Type of event
            message: Event message
            operator_id: ID of the operator
            order_number: Order number
            section_id: Section ID
            session_id: Session ID
            tempered_unlock: Tampered unlock status
            type: Event type

        Returns:
            True if journal data sent successfully, False otherwise
        """
        if not self.external_api_enabled:
            self.logger.info("External API is disabled, skipping journal data send")
            return True

        request_data = {
            "SerialNumber": serial_number,
            "Timestamp": timestamp,
            "EnteredPin": entered_pin or "",
            "EventResult": event_result or "",
            "EventType": event_type or "",
            "Message": message or "",
            "OperatorId": operator_id or "",
            "OrderNumber": order_number or "",
            "SectionId": section_id or "",
            "SessionId": session_id or "",
            "TemperedUnlock": tempered_unlock or "",
            "Type": type or ""
        }

        success, data = await self.send_to_server("/api/journal", request_data, "journal")
        return success


    def _save_failed_request(self, endpoint: str, action: str, payload: Dict[str, Any], method: str = "post"):
        """
        Save failed request to server_request_queue table.
        Saves important requests for retry, excludes heartbeat requests.
        Note: method column will be ignored if it doesn't exist in database.
        """
        # Save all requests except heartbeat (imalive) requests
        if endpoint == "/api/imalive":
            return

        try:
            conn = self._get_db_connection()
            cursor = conn.cursor()

            # Use the correct column name 'methond' as defined in the migration
            query = """
                INSERT INTO server_request_queue (
                    device_status, enpoint, action, error, payload, sent_attemps, methond
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            values = (
                1,  # device_status = 1 (active)
                endpoint,
                action,
                0,  # error = 0 (not successfully sent yet)
                json.dumps(payload),
                0,  # sent_attemps = 0 (initial)
                method
            )

            cursor.execute(query, values)
            conn.commit()
            self.logger.info(f"Failed request saved to queue: {endpoint}")

        except mysql.connector.Error as err:
            self.logger.error(f"Database error saving failed request: {err}")
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()

    async def server_ping(self) -> bool:
        """
        Check if server is available using /api/imalive endpoint.

        Returns:
            True if server is available and returns "success": true, False otherwise
        """
        if not self.external_api_enabled:
            return False

        try:
            url = f"{self.base_url}/api/imalive"        # later can be replaced by ping endpoint
            request_data = {
                "SerialNumber": self.serial_number
            }
            headers = {
                "jv-api-key": self.api_token,
                "Content-Type": "application/json"
            }
            timeout = aiohttp.ClientTimeout(total=self.timeout)

            # Create connector with proper DNS resolution settings
            connector = aiohttp.TCPConnector(
                limit=10,
                limit_per_host=5,
                ttl_dns_cache=300,
                use_dns_cache=True,
                enable_cleanup_closed=True
            )

            async with aiohttp.ClientSession(timeout=timeout, connector=connector) as session:
                async with session.post(url, json=request_data, headers=headers) as response:
                    if response.status == 200:
                        response_data = await response.json()
                        return response_data.get("success") is True
                    else:
                        return False

        except Exception as e:
            self.logger.info(f"Server ping failed: {e}")
            return False


    async def retry_failed_requests(self) -> int:
        """
        Retry failed requests from server_request_queue.
        Only retries if server is available.

        Returns:
            Number of requests successfully retried
        """
        if not self.external_api_enabled:
            return 0
        
        self.logger.info("Retrying failed requests")

        # Check if server is available
        if not await self.server_ping():
            self.logger.info("Server ping failed, skipping retry of failed requests")
            return 0

        try:
            conn = self._get_db_connection()
            cursor = conn.cursor()

            # Get failed requests that are ready to retry
            query = """
                SELECT id, enpoint, action, payload, sent_attemps, methond
                FROM server_request_queue
                WHERE error = 0 AND sent_attemps < 5
                ORDER BY created_at ASC
                LIMIT 10
            """
            cursor.execute(query)
            failed_requests = cursor.fetchall()

            successful_retries = 0

            for request_id, endpoint, action, payload_json, sent_attempts, method in failed_requests:
                try:
                    payload = json.loads(payload_json)

                    # Retry send failed request to server using send_to_server method
                    # Use stored method or default to "post" if None
                    retry_method = method if method else "post"
                    success, _ = await self.send_to_server(endpoint, payload, action, retry_method, save_to_queues=False)

                    if success:
                        # Mark as successfully sent (error = 1)
                        update_query = "UPDATE server_request_queue SET error = 1, sent_attemps = %s WHERE id = %s"
                        cursor.execute(update_query, (sent_attempts + 1, request_id))
                        successful_retries += 1
                        self.logger.info(f"Successfully retried request {request_id} to {endpoint}")
                        
                        # Change order reservations status in endpoint /order/operator/deliver
                        if endpoint == "/api/order/deliver":
                            order_number = payload.get("order_number")
                            if order_number:
                                self.logger.info(f"Changing order reservation status for order number {order_number}")
                                try:
                                    from infrastructure.repositories.order_repository import order_repository
                                    await order_repository.edit_reservations(order_number, 0)
                                except Exception as e:
                                    self.logger.error(f"Error updating order reservation status: {e}")
                    else:
                        # Increment sent_attempts
                        new_attempts = sent_attempts + 1
                        if new_attempts >= 5:
                            # Mark as unsuccessfully sent after 5 attempts (error = 2)
                            update_query = "UPDATE server_request_queue SET error = 2, sent_attemps = %s WHERE id = %s"
                            cursor.execute(update_query, (new_attempts, request_id))
                            self.logger.warning(f"Request {request_id} to {endpoint} marked as failed after 5 attempts")
                        else:
                            # Just increment attempts
                            update_query = "UPDATE server_request_queue SET sent_attemps = %s WHERE id = %s"
                            cursor.execute(update_query, (new_attempts, request_id))

                except json.JSONDecodeError:
                    self.logger.error(f"Invalid JSON payload in request {request_id}")
                    # Mark as error (error = 2 for invalid data)
                    update_query = "UPDATE server_request_queue SET error = 2 WHERE id = %s"
                    cursor.execute(update_query, (request_id,))

            conn.commit()

            if successful_retries > 0:
                self.logger.info(f"Successfully retried {successful_retries} failed requests")

            return successful_retries

        except mysql.connector.Error as err:
            self.logger.error(f"Database error during retry: {err}")
            return 0
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()



    async def storage_change_status(
        self,
        reservation_uuid: str,
        serial_number: Optional[str] = None,
        reservation_pin: Optional[str] = None,
        section_id: Optional[int] = None,
        email: Optional[str] = None,
        size_category: Optional[int] = None,
        paid_price: Optional[float] = None,
        paid_fine: Optional[float] = None,
        timestamp: Optional[str] = None,
        action: Optional[int] = None,
        status: Optional[int] = None
    ) -> bool:
        """
        Send storage change status data to Jetveo API /api/product-change-status endpoint.
        Only sends if external API is enabled.

        Args:
            reservation_uuid: Reservation UUID (required)
            serial_number: Device serial number (defaults to env SERIAL_NUMBER)
            reservation_pin: Reservation PIN (optional)
            section_id: Section ID (optional)
            email: Customer email (optional)
            size_category: Size category (optional)
            paid_price: Paid price amount (optional)
            paid_fine: Paid fine amount (optional)
            timestamp: Timestamp in ISO format (optional)
            action: Action code (optional)
            status: Status code (optional)

        Returns:
            True if successful, False otherwise
        """
        if not self.external_api_enabled:
            self.logger.info("External API is disabled, skipping storage change status")
            return False

        # Use default serial number if not provided
        if serial_number is None:
            serial_number = self.serial_number

        # Use current timestamp if not provided
        if timestamp is None:
            from datetime import datetime, timezone
            timestamp = datetime.now(timezone.utc).isoformat().replace('+00:00', 'Z')

        request_data = {
            "SerialNumber": serial_number,
            "ReservationUuid": reservation_uuid,
            "Timestamp": timestamp
        }

        if reservation_pin:
            request_data["ReservationPin"] = reservation_pin
        if section_id:
            request_data["SectionId"] = int(section_id)
        if email:
            request_data["Email"] = email
        if size_category:
            request_data["SizeCategory"] = int(size_category)
        if paid_price:
            request_data["PaidPrice"] = paid_price
        if paid_fine:
            request_data["PaidFine"] = paid_fine
        if action:
            request_data["Action"] = int(action)
        if status:
            request_data["Status"] = int(status)
        

        success, data = await self.send_to_server("/api/storage-change-status", request_data, "storage_change_status")
        if data != None and "success" in data and data["success"]:
            return True
        return False


    async def product_change_status(
        self,
        reservation_uuid: str,
        serial_number: Optional[str] = None,
        reserved: Optional[bool] = None,
        reservation_pin: Optional[str] = None,
        section_id: Optional[int] = None,
        timestamp: Optional[str] = None,
        price: Optional[float] = None,
        ean: Optional[str] = None,
        action: Optional[int] = None,
        status: Optional[int] = None
    ) -> bool:
        """
        Send product change status data to Jetveo API /api/product-change-status endpoint.
        Only sends if external API is enabled.

        Args:
            reservation_uuid: Reservation UUID (required)
            serial_number: Device serial number (defaults to env SERIAL_NUMBER)
            reserved: Whether the product is reserved (optional)
            reservation_pin: Reservation PIN (optional)
            section_id: Section ID (optional)
            timestamp: Timestamp in ISO format (optional)
            price: Product price (optional)
            ean: Product EAN code (optional)
            action: Action code (optional)
            status: Status code (optional)

        Returns:
            True if successful, False otherwise
        """
        if not self.external_api_enabled:
            self.logger.info("External API is disabled, skipping product change status")
            return False

        # Use default serial number if not provided
        if serial_number is None:
            serial_number = self.serial_number

        # Use current timestamp if not provided
        if timestamp is None:
            from datetime import datetime, timezone
            timestamp = datetime.now(timezone.utc).isoformat().replace('+00:00', 'Z')

        request_data = {
            "SerialNumber": serial_number,
            "ReservationUuid": reservation_uuid,
            "Timestamp": timestamp,
            "Action": action or 0,
            "Status": status or 0
        }

        if section_id:
            request_data["SectionId"] = int(section_id)
        if price:
            request_data["Price"] = price
        if ean:
            request_data["Ean"] = ean
        if reserved:
            request_data["Reserved"] = reserved
        if reservation_pin:
            request_data["ReservationPin"] = reservation_pin


        success, data = await self.send_to_server("/api/product-change-status", request_data, "product_change_status")
        if data != None and "success" in data and data["success"]:
            return True
        return False        

    async def send_to_server(self, endpoint: str, payload: Dict[str, Any], action: str, method: str = "post", save_to_queues: bool = True) -> Tuple[bool, Dict[str, Any]]:
        """
        Function to send payload to entered endpoint on jetveo_server
        """
        if not self.external_api_enabled:
            return True, {}

        try:
            url = f"{self.base_url}{endpoint}"
            headers = {
                "jv-api-key": self.api_token,
                "Content-Type": "application/json"
            }
            timeout = aiohttp.ClientTimeout(total=self.timeout)

            # Create connector with proper DNS resolution settings
            connector = aiohttp.TCPConnector(
                limit=10,
                limit_per_host=5,
                ttl_dns_cache=300,
                use_dns_cache=True,
                enable_cleanup_closed=True
            )

            async with aiohttp.ClientSession(timeout=timeout, connector=connector) as session:
                async with session.request(method, url, json=payload, headers=headers) as response:
                    if response.status == 200:
                        response_data = await response.json()
                        self.logger.info(f"Request on server send succesfully: {response_data} for {endpoint} with payload: {payload}")
                        return True, response_data
                    else:
                        self.logger.error(f"Request on server failed with status {response.status} for {endpoint} with payload: {payload}")
                        if endpoint not in ["/api/imalive"] and save_to_queues:
                            self._save_failed_request(endpoint, action, payload, method)
                        return False, None

        except asyncio.TimeoutError:
            self.logger.error(f"Request on server failed with timeout for {endpoint} with payload {payload}")
            if save_to_queues:
                self._save_failed_request(endpoint, action, payload, method)
            return False, None
        except Exception as e:
            self.logger.error(f"Error sending request to server: {e} for {endpoint} with payload {payload}")
            if save_to_queues:
                self._save_failed_request(endpoint, action, payload, method)
            return False, None



    async def send_temperature_electronic(self, sensors: List[Dict[str, Any]]) -> bool:
        """
        Send electronic temperature sensor data to Jetveo API.

        Args:
            sensors: List of sensor dictionaries in format [{"id": "1", "value": 23.5}, {"id": "2", "value": 24.1}]

        Returns:
            True if successful, False otherwise

        Expected payload format:
        {
            "serial_number": "123456-789",
            "timestamp": "1971-01-28T13:39:08.015Z",
            "sensors": [
                {"id": "1", "value": 5},
                {"id": "2", "value": 6}
            ]
        }
        """
        from datetime import datetime, timezone

        # Check if sensors is None or empty
        if not sensors:
            self.logger.info("No electronic sensor data to send")
            return True

        # Sensors array is already in the correct format, just ensure proper types
        sensors_array = []
        for sensor_item in sensors:
            sensors_array.append({
                "id": str(sensor_item["id"]),
                "value": sensor_item["value"]
            })

        # Prepare payload
        payload = {
            "serial_number": self.serial_number,
            "timestamp": datetime.now(timezone.utc).isoformat().replace('+00:00', 'Z'),
            "sensors": sensors_array
        }

        success, data = await self.send_to_server("/api/post-temperature-electronic", payload, "temperature_electronic")
        if data != None and "success" in data and data["success"]:
            return True
        return False

    async def send_temperature_comet(self, sensors: List[Dict[str, Any]]) -> bool:
        """
        Send Comet temperature sensor data to Jetveo API.

        Args:
            sensors: List of sensor dictionaries in format [{"id": "*************", "value": 21.3}, {"id": "*************", "value": 22.1}]

        Returns:
            True if successful, False otherwise

        Expected payload format:
        {
            "serial_number": "123456-789",
            "timestamp": "1971-01-28T13:39:08.015Z",
            "sensors": [
                {"id": "*************", "value": 21.3},
                {"id": "*************", "value": 22.1}
            ]
        }
        """
        from datetime import datetime, timezone

        # Check if sensors is None or empty
        if not sensors:
            self.logger.info("No Comet sensor data to send")
            return True

        # Sensors array is already in the correct format, just ensure proper types
        sensors_array = []
        for sensor_item in sensors:
            sensors_array.append({
                "id": str(sensor_item["id"]),
                "value": sensor_item["value"]
            })

        # Prepare payload
        payload = {
            "serial_number": self.serial_number,
            "timestamp": datetime.now(timezone.utc).isoformat().replace('+00:00', 'Z'),
            "sensors": sensors_array
        }

        success, _ = await self.send_to_server("/api/post-temperature-comet", payload, "temperature_comet")
        return success


    def get_order_list(self) -> List[Dict[str, Any]]:
        """
        function to get order list from jetveo server.
        """
        
        payload = {
            "serial_number": self.serial_number
        }
        success, data = self.send_to_server("/api/order/list", payload, "order_list", method="get", save_to_queues=False)
        return data







# Global client instance
jetveo_client = JetveoClient()
    

def storage_change_status_async(
    reservation_uuid: str,
    serial_number: str | None = None,
    reservation_pin: str | None = None,
    section_id: int | None = None,
    email: str | None = None,
    size_category: int | None = None,
    paid_price: float | None = None,
    paid_fine: float | None = None,
    timestamp: str | None = None,
    action: int | None = None,
    status: int | None = None,
    ):
    """
    Fire-and-forget wrapper for async storage_change_status.
    """

    async def _call_async():
        try:
            await jetveo_client.storage_change_status(
                reservation_uuid=reservation_uuid,
                serial_number=serial_number,
                reservation_pin=reservation_pin,
                section_id=section_id,
                email=email,
                size_category=size_category,
                paid_price=paid_price,
                paid_fine=paid_fine,
                timestamp=timestamp,
                action=action,
                status=status,
            )
        except Exception as e:
            logger.error(f"Error in storage_change_status_async: {e}")

    try:
        loop = asyncio.get_running_loop()
        asyncio.create_task(_call_async())
    except RuntimeError:
        # If there's no running event loop, run in a new thread
        asyncio.to_thread(lambda: asyncio.run(_call_async()))




def product_change_status_async(
    reservation_uuid: str,
    serial_number: Optional[str] = None,
    reserved: Optional[bool] = None,
    reservation_pin: Optional[str] = None,
    section_id: Optional[int] = None,
    timestamp: Optional[str] = None,
    price: Optional[float] = None,
    ean: Optional[str] = None,
    action: Optional[int] = None,
    status: Optional[int] = None,
    ):
    """
    Fire-and-forget wrapper for async product_change_status.
    """

    async def _call_async():
        try:
            await jetveo_client.product_change_status(
                reservation_uuid=reservation_uuid,
                serial_number=serial_number,
                reserved=reserved,
                reservation_pin=reservation_pin,
                section_id=section_id,
                timestamp=timestamp,
                price=price,
                ean=ean,
                action=action,
                status=status,
            )
        except Exception as e:
            logger.error(f"Error in product_change_status_async: {e}")

    try:
        loop = asyncio.get_running_loop()
        asyncio.create_task(_call_async())
    except RuntimeError:
        # If there's no running event loop, run in a new thread
        asyncio.to_thread(lambda: asyncio.run(_call_async()))




def start_jetveo_echo_loop():
    """
    Start the Jetveo echo loop service.
    Call this function during application startup.
    """
    jetveo_client.start_echo_loop()

def stop_jetveo_echo_loop():
    """
    Stop the Jetveo echo loop service.
    Call this function during application shutdown.
    """
    jetveo_client.stop_echo_loop()

