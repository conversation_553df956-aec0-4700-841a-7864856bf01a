import mysql.connector
from os import getenv
from typing import List, Dict, Any, Optional
from uuid import uuid4
from managers.logger_manager import logger

from managers.logger_manager import logger

def get_db():
    return mysql.connector.connect(
        host=getenv("DB_HOST"),
        port=int(getenv("DB_PORT")),
        database=getenv("DB_NAME"),
        user=getenv("DB_USER"),
        password=getenv("DB_PASSWORD")
    )

class OrderRepository:
    """Repository for order database operations"""
    
    def __init__(self):
        self.logger = logger
        

    
    def get_expired_order_sections(self) -> List[int]:
        """
        Get all sections with expired orders.
        Returns all section_ids from order_reservations where expired=0 and status=1 and type='employee_send'
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            query = """
                SELECT DISTINCT CAST(section_id AS UNSIGNED) as section_id 
                FROM order_reservations 
                WHERE expired = 0 AND status = 1 AND type = 'employee_send'
                ORDER BY section_id
            """
            cursor.execute(query)
            results = cursor.fetchall()
            return [row['section_id'] for row in results]
        except Exception as e:
            self.logger.error(f"Error getting expired order sections: {e}")
            return []
        finally:
            cursor.close()
            db.close()
    
    def get_employee_order_sections(self) -> List[int]:
        """
        Get all sections with employee orders.
        Returns all section_ids from order_reservations where expired=0 and status=1 and type='employee_send'
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            query = """
                SELECT DISTINCT CAST(section_id AS UNSIGNED) as section_id 
                FROM order_reservations 
                WHERE expired = 0 AND status = 1 AND type = 'employee_send'
                ORDER BY section_id
            """
            cursor.execute(query)
            results = cursor.fetchall()
            return [row['section_id'] for row in results]
        except Exception as e:
            self.logger.error(f"Error getting employee order sections: {e}")
            return []
        finally:
            cursor.close()
            db.close()
    
    def create_employee_delivery_reservation(self, phone_number: str, section_id: int) -> Dict[str, Any]:
        """
        Create a new order reservation for employee delivery.
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            # Generate unique UUID and PIN
            reservation_uuid = str(uuid4())
            from .pin_generator import generate_pin
            pickup_pin = generate_pin()
            
            if pickup_pin is None:
                return {"success": False, "error": "Failed to generate unique PIN"}
            
            # Get box_uuid from environment
            box_uuid = getenv("BOX_UUID", "default-box")
            
            query = """
                INSERT INTO order_reservations 
                (uuid, box_uuid, section_id, status, pickup_pin, phone_number, expired, type, created_at, last_update)
                VALUES (%s, %s, %s, 1, %s, %s, 0, 'employee_deliver', NOW(), NOW())
            """
            cursor.execute(query, (reservation_uuid, box_uuid, str(section_id), pickup_pin, phone_number))
            db.commit()
            
            return {
                "success": True,
                "reservation_id": cursor.lastrowid,
                "pickup_pin": pickup_pin,
                "section_id": section_id
            }
        except Exception as e:
            db.rollback()
            self.logger.error(f"Error creating employee delivery reservation: {e}")
            return {"success": False, "error": str(e)}
        finally:
            cursor.close()
            db.close()
    
    def create_employee_send_reservation(self, phone_number: str, section_id: int) -> Dict[str, Any]:
        """
        Create a new order reservation for employee sending order.
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            # Generate unique UUID and PIN
            reservation_uuid = str(uuid4())
            from .pin_generator import generate_pin
            insert_pin = generate_pin()
            
            if insert_pin is None:
                return {"success": False, "error": "Failed to generate unique PIN"}
            
            # Get box_uuid from environment
            box_uuid = getenv("BOX_UUID", "default-box")
            
            query = """
                INSERT INTO order_reservations 
                (uuid, box_uuid, section_id, status, insert_pin, phone_number, expired, type, created_at, last_update)
                VALUES (%s, %s, %s, 1, %s, %s, 0, 'employee_send', NOW(), NOW())
            """
            cursor.execute(query, (reservation_uuid, box_uuid, str(section_id), insert_pin, phone_number))
            db.commit()
            
            return {
                "success": True,
                "reservation_id": cursor.lastrowid,
                "insert_pin": insert_pin,
                "section_id": section_id
            }
        except Exception as e:
            db.rollback()
            self.logger.error(f"Error creating employee send reservation: {e}")
            return {"success": False, "error": str(e)}
        finally:
            cursor.close()
            db.close()
    



    def create_order_reservation(self, phone_number: str = None, section_id: int = None, status: int = None,
                                 lookup_pin: str = None, insert_pin: str = None, pickup_pin: str = None,
                                 auto_generate_pin: bool = True) -> Dict[str, Any]:
        """
        Unified method to create order reservations with flexible parameters.

        Args:
            phone_number: Phone number for the reservation (required unless lookup_pin is provided)
            section_id: Section ID for the reservation
            status: Status code for the reservation
            lookup_pin: PIN to lookup existing reservation for phone number (for customer_send operations)
            insert_pin: Specific insert PIN to use (overrides auto-generation)
            pickup_pin: Specific pickup PIN to use
            auto_generate_pin: Whether to auto-generate insert_pin if not provided

        Returns:
            Dict with success status, reservation details, and any errors
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            # If lookup_pin is provided, find phone number from existing reservation
            if lookup_pin and not phone_number:
                cursor.execute("""
                    SELECT phone_number FROM order_reservations
                    WHERE insert_pin = %s OR pickup_pin = %s
                    ORDER BY created_at DESC LIMIT 1
                """, (lookup_pin, lookup_pin))

                existing_reservation = cursor.fetchone()
                if not existing_reservation:
                    return {"success": False, "error": "No reservation found with this PIN"}

                phone_number = existing_reservation['phone_number']

            # Validate required parameters
            if not phone_number or section_id is None or status is None:
                return {"success": False, "error": "Missing required parameters: phone_number, section_id, status"}

            # Generate unique UUID
            reservation_uuid = str(uuid4())

            # Handle insert_pin generation/assignment
            self.logger.info(f"create_order_reservation called with: phone_number={phone_number}, section_id={section_id}, status={status}, insert_pin='{insert_pin}', pickup_pin='{pickup_pin}', auto_generate_pin={auto_generate_pin}")

            # For reclaim operations (status=3), never auto-generate insert_pin
            if status == 3:
                self.logger.info(f"Reclaim operation detected (status=3), using provided insert_pin: '{insert_pin}' without auto-generation")
            elif auto_generate_pin and not insert_pin:
                from .pin_generator import generate_pin
                insert_pin = generate_pin()
                self.logger.info(f"Generated new insert_pin: '{insert_pin}'")
                if insert_pin is None:
                    return {"success": False, "error": "Failed to generate unique PIN"}
            else:
                self.logger.info(f"Using provided insert_pin: '{insert_pin}' (auto_generate_pin={auto_generate_pin})")

            # Get box_uuid from environment
            box_uuid = getenv("BOX_UUID", "default-box")

            # Determine type based on status
            type_mapping = {
                1: "delivered",
                2: "employee_send",
                3: "customer_reclaim",
                4: "customer_send"
            }
            reservation_type = type_mapping.get(status, "unknown")

            # Build query dynamically based on available fields
            fields = ["uuid", "box_uuid", "section_id", "status", "phone_number", "expired", "type", "created_at", "last_update"]
            values = [reservation_uuid, box_uuid, str(section_id), status, phone_number, 0, reservation_type, "NOW()", "NOW()"]
            placeholders = ["%s", "%s", "%s", "%s", "%s", "%s", "%s", "NOW()", "NOW()"]

            if insert_pin is not None:
                fields.insert(-3, "insert_pin")  # Insert before created_at, last_update, expired
                values.insert(-3, insert_pin)
                placeholders.insert(-3, "%s")

            if pickup_pin is not None:
                fields.insert(-3, "pickup_pin")  # Insert before created_at, last_update, expired
                values.insert(-3, pickup_pin)
                placeholders.insert(-3, "%s")

            query = f"""
                INSERT INTO order_reservations ({', '.join(fields)})
                VALUES ({', '.join(placeholders)})
            """

            # Remove NOW() from values for execution
            exec_values = [v for v in values if v != "NOW()"]
            cursor.execute(query, exec_values)
            db.commit()

            result = {
                "success": True,
                "reservation_id": cursor.lastrowid,
                "section_id": section_id,
                "uuid": reservation_uuid,
                "phone_number": phone_number
            }

            if insert_pin:
                result["insert_pin"] = insert_pin
            if pickup_pin:
                result["pickup_pin"] = pickup_pin

            self.logger.info(f"Created order reservation: UUID={reservation_uuid}, phone={phone_number}, section={section_id}, status={status}, insert_pin={insert_pin}, pickup_pin={pickup_pin}")
            return result

        except Exception as e:
            db.rollback()
            self.logger.error(f"Error creating order reservation: {e}")
            return {"success": False, "error": str(e)}
        finally:
            cursor.close()
            db.close()












    async def handle_section_open(self, session_id: str, section_id: int, endpoint_type: str) -> bool:
        """Thuis function is called in pickup_process() after unlocking each seciton."""
        match endpoint_type:
            case "order/operator/pickup-expired":
                # TODO: add log_timeline_event()
                return self.edit_reservations(find_section_id=section_id, change_status=0)     # deactivate order reservation
            case "order/operator/pickup":
                # TODO: add log_timeline_event()
                return self.edit_reservations(find_section_id=section_id, change_status=0)     # deactivate order reservation
            case "order/customer/pickup":
                # TODO: add log_timeline_event()
                return self.edit_reservations(find_section_id=section_id, change_status=0)     # deactivate order reservation
            case _:
                logger.info(f"Unknown endpoint type for order handle_section_open: {endpoint_type}")
                return True



    async def create_reservation(
        self,
        order_uuid: str = None,     # if None, it will automaticky generate
        order_number: str = None,
        serial_number: str = None,
        status: int = 1,
        seciton_id: int = None,
        insert_pin: str = None,
        package_pin: str = None,
        pickup_pin: str = None,
        card_nubmer: str = None,
        age_control_required: bool = False,
        age_controlled: bool = 0,
        phone_nubmer: str = None,
        max_days: int = None,       # max days till it reservation expires
        payment_required: bool = False,
        price: float = 0,
        paid_status: bool = 0,      # if order was paid
        expired: int = 0,           # if order is expired
        type: str = None,           # type of order (magna, pradelna, etc.)
        section_can_change: int = 0     # if courier can change the section (0 = no, 1 = yes)
        ) -> Optional[Dict[str, Any]]:

        """
        Function to create new record in table order_reservations.

        Returns:
            Dict[str, Any]: Reservation data if successful, None if failed
        """

        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            # Generate UUID if not provided
            if order_uuid is None:
                order_uuid = str(uuid4())

            # Generate PINs if not provided
            if insert_pin is None:
                from .pin_generator import generate_pin
                insert_pin = generate_pin()
                if insert_pin is None:
                    self.logger.error("Failed to generate unique insert PIN")
                    return None

            if pickup_pin is None:
                from .pin_generator import generate_pin
                pickup_pin = generate_pin()
                if pickup_pin is None:
                    self.logger.error("Failed to generate unique pickup PIN")
                    return None

            # Build the INSERT query
            query = """
                INSERT INTO order_reservations (
                    order_uuid, order_number, serial_number, status, section_id,
                    insert_pin, package_pin, pickup_pin, card_number,
                    age_control_required, age_controlled, phone_number, max_days,
                    price, payment_required, paid_status, expired, type,
                    sectionc_can_change, created_at, last_update
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, NOW(), NOW()
                )
            """

            # Execute the query
            cursor.execute(query, (
                order_uuid, order_number, serial_number, status, seciton_id,
                insert_pin, package_pin, pickup_pin, card_nubmer,
                age_control_required, age_controlled, phone_nubmer, max_days,
                price, payment_required, paid_status, expired, type,
                section_can_change
            ))
            db.commit()

            reservation_id = cursor.lastrowid

            # Return success with reservation data
            reservation_data = {
                "id": reservation_id,
                "order_uuid": order_uuid,
                "order_number": order_number,
                "serial_number": serial_number,
                "status": status,
                "section_id": seciton_id,
                "insert_pin": insert_pin,
                "package_pin": package_pin,
                "pickup_pin": pickup_pin,
                "card_number": card_nubmer,
                "age_control_required": age_control_required,
                "age_controlled": age_controlled,
                "phone_number": phone_nubmer,
                "max_days": max_days,
                "price": price,
                "payment_required": payment_required,
                "paid_status": paid_status,
                "expired": expired,
                "type": type,
                "section_can_change": section_can_change
            }

            self.logger.info(f"Created order reservation: ID={reservation_id}, UUID={order_uuid}, phone={phone_nubmer}, section={seciton_id}")
            return reservation_data

        except Exception as e:
            db.rollback()
            self.logger.error(f"Error creating order reservation: {e}")
            return None
        finally:
            cursor.close()
            db.close()






    async def edit_reservations(
        self,

        # Search parameters - can be used to find reservation instead of order_uuid
        reservation_id: Optional[int] = None,
        order_uuid: Optional[str] = None,
        order_number: Optional[str] = None,
        serial_number: Optional[str] = None,
        find_section_id: Optional[int] = None,
        find_status: Optional[int] = 1,

        # Update parameters
        change_status: Optional[int] = None,
        change_section_id: Optional[int] = None,
        insert_pin: Optional[str] = None,
        package_pin: Optional[str] = None,
        pickup_pin: Optional[str] = None,
        card_number: Optional[str] = None,
        age_control_required: Optional[bool] = None,
        age_controlled: Optional[bool] = None,
        phone_number: Optional[str] = None,
        price: Optional[float] = None,  # New parameter for price editing
        ) -> List[Dict[str, Any]]:

        """
        Function to edit records in table order_reservations.

        Params:
            reservation_id: ID of reservation which I want to edit (primary search parameter)
            order_uuid: UUID of reservation which I want to edit (optional if using other search params)
            order_number, serial_number, find_section_id: Alternative search parameters
            price: New parameter for price editing
            all other: all other params are optional, if provided, they will be updated

        Returns:
            List[Dict[str, Any]]: List of dictionaries with entered parameters for each updated reservation
        """

        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            # Build WHERE clause based on search parameters
            where_conditions = []
            where_params = []

            if reservation_id:
                where_conditions.append("id = %s")
                where_params.append(reservation_id)
            elif order_uuid:
                where_conditions.append("order_uuid = %s")
                where_params.append(order_uuid)
            elif order_number:
                where_conditions.append("order_number = %s")
                where_params.append(order_number)
            elif serial_number:
                where_conditions.append("serial_number = %s")
                where_params.append(serial_number)
            elif find_section_id:
                where_conditions.append("section_id = %s")
                where_params.append(find_section_id)
            elif find_status:
                where_conditions.append("status = %s")
                where_params.append(find_status)
            else:
                self.logger.error("At least one search parameter must be provided")
                return []

            # Build the UPDATE query dynamically based on provided parameters
            update_fields = []
            update_values = []
            result_data = {}

            if change_status is not None:
                update_fields.append("status = %s")
                update_values.append(change_status)
                result_data["status"] = change_status

            if change_section_id is not None:
                update_fields.append("section_id = %s")
                update_values.append(change_section_id)
                result_data["section_id"] = change_section_id

            if insert_pin is not None:
                update_fields.append("insert_pin = %s")
                update_values.append(insert_pin)
                result_data["insert_pin"] = insert_pin

            if package_pin is not None:
                update_fields.append("package_pin = %s")
                update_values.append(package_pin)
                result_data["package_pin"] = package_pin

            if pickup_pin is not None:
                update_fields.append("pickup_pin = %s")
                update_values.append(pickup_pin)
                result_data["pickup_pin"] = pickup_pin

            if card_number is not None:
                update_fields.append("card_number = %s")
                update_values.append(card_number)
                result_data["card_number"] = card_number

            if age_control_required is not None:
                update_fields.append("age_control_required = %s")
                update_values.append(age_control_required)
                result_data["age_control_required"] = age_control_required

            if age_controlled is not None:
                update_fields.append("age_controlled = %s")
                update_values.append(age_controlled)
                result_data["age_controlled"] = age_controlled

            if phone_number is not None:
                update_fields.append("phone_number = %s")
                update_values.append(phone_number)
                result_data["phone_number"] = phone_number

            if price is not None:
                update_fields.append("price = %s")
                update_values.append(float(price))
                result_data["price"] = float(price)

            # Always update last_update timestamp
            update_fields.append("last_update = NOW()")

            if len(update_fields) == 1:  # Only last_update field
                self.logger.error("No fields provided for update")
                return []

            # Build and execute the UPDATE query
            where_clause = " AND ".join(where_conditions)
            query = f"UPDATE order_reservations SET {', '.join(update_fields)} WHERE {where_clause}"
            all_params = update_values + where_params

            cursor.execute(query, all_params)
            db.commit()

            updated_count = cursor.rowcount
            if updated_count == 0:
                self.logger.error("No rows were updated")
                return []

            # Add search parameters to result data
            if reservation_id:
                result_data["reservation_id"] = reservation_id
            if order_uuid:
                result_data["order_uuid"] = order_uuid
            if order_number:
                result_data["order_number"] = order_number
            if serial_number:
                result_data["serial_number"] = serial_number
            if find_section_id:
                result_data["find_section_id"] = find_section_id

            # Return list with the result data for each updated row
            result_list = [result_data.copy() for _ in range(updated_count)]

            self.logger.info(f"Updated {updated_count} order reservation(s) with fields: {list(result_data.keys())}")
            return result_list

        except Exception as e:
            db.rollback()
            self.logger.error(f"Error editing order reservations: {e}")
            return []
        finally:
            cursor.close()
            db.close()




    async def find_reservations(
        self,
        reservation_id: Optional[int] = None,
        order_uuid: str = None,
        order_number: str = None,
        serial_number: str = None,
        status: int = None,
        seciton_id: int = None,
        insert_pin: str = None,
        package_pin: str = None,
        pickup_pin: str = None,
        phone_nubmer: str = None,
        ) -> List[Dict[str, Any]]:

        """
        Function to find records in table order_reservations.

        Params:
            all params are optional, if provided, they will be used to search for reservations

        Returns:
            List[Dict[str, Any]]: List of found reservations data, empty list if none found
        """

        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            # Build WHERE clause dynamically based on provided parameters
            where_conditions = []
            where_params = []

            if reservation_id is not None:
                where_conditions.append("id = %s")
                where_params.append(reservation_id)

            if order_uuid is not None:
                where_conditions.append("order_uuid = %s")
                where_params.append(order_uuid)

            if order_number is not None:
                where_conditions.append("order_number = %s")
                where_params.append(order_number)

            if serial_number is not None:
                where_conditions.append("serial_number = %s")
                where_params.append(serial_number)

            if status is not None:
                where_conditions.append("status = %s")
                where_params.append(status)

            if seciton_id is not None:
                where_conditions.append("section_id = %s")
                where_params.append(seciton_id)

            if insert_pin is not None:
                where_conditions.append("insert_pin = %s")
                where_params.append(insert_pin)

            if package_pin is not None:
                where_conditions.append("package_pin = %s")
                where_params.append(package_pin)

            if pickup_pin is not None:
                where_conditions.append("pickup_pin = %s")
                where_params.append(pickup_pin)

            if phone_nubmer is not None:
                where_conditions.append("phone_number = %s")
                where_params.append(phone_nubmer)

            # If no search criteria provided, return empty list
            if not where_conditions:
                self.logger.warning("No search criteria provided for find_reservations")
                return []

            # Build and execute the SELECT query
            where_clause = " AND ".join(where_conditions)
            query = f"SELECT * FROM order_reservations WHERE {where_clause} ORDER BY created_at DESC"

            cursor.execute(query, where_params)
            results = cursor.fetchall()

            if results:
                self.logger.info(f"Found {len(results)} reservations with provided criteria: {len(where_conditions)} conditions")
                return results
            else:
                self.logger.info(f"No reservations found with provided criteria: {len(where_conditions)} conditions")
                return []

        except Exception as e:
            self.logger.error(f"Error finding order reservations: {e}")
            return []
        finally:
            cursor.close()
            db.close()





# Global repository instance
order_repository = OrderRepository()
