import ssl
import json
import paho.mqtt.client as mqtt

MQTT_HOST = "mqtt.3idlabs.eu"
MQTT_PORT = 1883  #8883
MQTT_USERNAME = "b-003"
MQTT_PASSWORD = "ECE27m8YMR"
MQTT_CLIENT_ID = "b-003"  # přesně podle požadavku

TOPIC_WILDCARD = "devices/#"

# ---- Callbacks ----
def on_connect(client, userdata, flags, reason_code, properties=None):
    print(f"[CONNECTED] Reason code: {reason_code}")
    if reason_code == 0:
        client.subscribe(TOPIC_WILDCARD, qos=1)
        print(f"[SUBSCRIBED] {TOPIC_WILDCARD}")

def on_message(client, userdata, msg):
    payload = msg.payload.decode("utf-8", errors="ignore")
    print(f"[MESSAGE] {msg.topic}: {payload}")
    reply = f"Odpověď: {payload}"
    client.publish(msg.topic, reply, qos=1, retain=True)
    print(f"[REPLY] -> {msg.topic}: {reply}")

def on_disconnect(client, userdata, reason_code, properties=None):
    print(f"[DISCONNECTED] Reason code: {reason_code}")

def on_log(client, userdata, level, buf):
    print(f"[LOG] {buf}")

# ---- MQTT Client ----
client = mqtt.Client(client_id=MQTT_CLIENT_ID, protocol=mqtt.MQTTv5)
client.username_pw_set(MQTT_USERNAME, MQTT_PASSWORD)
# TLS nastavení s validním certifikátem brokera (Let's Encrypt)
# client.tls_set(cert_reqs=ssl.CERT_REQUIRED)
# client.tls_insecure_set(False)

client.on_connect = on_connect
client.on_message = on_message
client.on_disconnect = on_disconnect
client.on_log = on_log

print("[CONNECTING]")
client.connect(MQTT_HOST, MQTT_PORT, keepalive=60)
client.loop_forever()
