import ssl
import json
import os
from dotenv import load_dotenv
import paho.mqtt.client as mqtt

# Load environment variables
load_dotenv()

# MQTT Configuration - use same approach as mqtt/client.py
MQTT_HOST = "mqtt.3idlabs.eu"
MQTT_PORT = 8883
MQTT_USERNAME = "b-003"
MQTT_PASSWORD = "ECE27m8YMR"
MQTT_CLIENT_ID = "b-003"

# Use CLIENT_ID for topics to match mqtt/client.py approach
CLIENT_ID = MQTT_CLIENT_ID
BASE_TOPIC = f"devices/{CLIENT_ID}/commands"
BASE_RESPONSE_TOPIC = f"devices/{CLIENT_ID}/responses"
TOPIC_WILDCARD = f"{BASE_TOPIC}/+/#"

# Connection state tracking
is_connected = False
is_running = False

# ---- Callbacks ----
def on_connect(client, userdata, flags, rc):
    """Callback for when the client connects to the broker - using VERSION1 callback signature"""
    global is_connected
    print(f"[CONNECTED] Return code: {rc}")
    if rc == 0:
        is_connected = True
        client.subscribe(TOPIC_WILDCARD, qos=1)
        print(f"[SUBSCRIBED] {TOPIC_WILDCARD}")
    else:
        is_connected = False
        print(f"[CONNECTION FAILED] Return code: {rc}")

def on_message(client, userdata, msg):
    """Callback for when a message is received from the broker"""
    try:
        payload = msg.payload.decode("utf-8", errors="ignore")
        print(f"[MESSAGE] {msg.topic}: {payload}")

        # Convert command topic to response topic (same as mqtt/client.py)
        response_topic = msg.topic.replace("/commands/", "/responses/")
        reply = f"Odpověď: {payload}"

        client.publish(response_topic, reply, qos=1, retain=True)
        print(f"[REPLY] -> {response_topic}: {reply}")
    except Exception as e:
        print(f"[ERROR] Processing message: {e}")

def on_disconnect(client, userdata, rc):
    """Callback for when the client disconnects from the broker"""
    global is_connected
    is_connected = False
    print(f"[DISCONNECTED] Return code: {rc}")

def on_log(client, userdata, level, buf):
    """Callback for logging"""
    print(f"[LOG] {buf}")

# def setup_client():
#     """Setup MQTT client with proper configuration - same approach as mqtt/client.py"""
#     # Use VERSION1 callback API for compatibility
#     client = mqtt.Client(mqtt.CallbackAPIVersion.VERSION1, client_id=MQTT_CLIENT_ID)
#     client.username_pw_set(MQTT_USERNAME, MQTT_PASSWORD)

#     # Set callbacks
#     client.on_connect = on_connect
#     client.on_message = on_message
#     client.on_disconnect = on_disconnect
#     client.on_log = on_log

#     return client

def setup_client():
    client = mqtt.Client(mqtt.CallbackAPIVersion.VERSION1, client_id=MQTT_CLIENT_ID)
    client.username_pw_set(MQTT_USERNAME, MQTT_PASSWORD)

    # --- TLS / SSL setup ---
    client.tls_set(cert_reqs=ssl.CERT_NONE)  # Pokud nemáš CA certifikát
    client.tls_insecure_set(True)  # Povolit neověřené certifikáty (nebezpečné na produkci)

    client.on_connect = on_connect
    client.on_message = on_message
    client.on_disconnect = on_disconnect
    client.on_log = on_log

    return client


def main():
    """Main function to start MQTT client"""
    global is_running

    print(f"[INIT] Connecting to {MQTT_HOST}:{MQTT_PORT} as {MQTT_CLIENT_ID}")
    print(f"[INIT] Subscribing to: {TOPIC_WILDCARD}")
    print(f"[INIT] Response topic base: {BASE_RESPONSE_TOPIC}")

    client = setup_client()

    try:
        print("[CONNECTING]")
        client.connect(MQTT_HOST, MQTT_PORT, keepalive=60)
        is_running = True

        # Start the network loop
        client.loop_forever()

    except KeyboardInterrupt:
        print("\n[SHUTDOWN] Keyboard interrupt received")
    except Exception as e:
        print(f"[ERROR] Connection failed: {e}")
    finally:
        is_running = False
        if client:
            try:
                client.disconnect()
                client.loop_stop()
            except:
                pass
        print("[SHUTDOWN] MQTT client stopped")

if __name__ == "__main__":
    main()
