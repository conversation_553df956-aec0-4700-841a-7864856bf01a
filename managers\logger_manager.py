import logging
import os

RESET = "\033[0m"
RED = "\033[31m"
BLUE = "\033[34m"
YELLOW = "\033[33m"
GRAY = "\033[90m"
GREEN = "\033[32m"

PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # root složka projektu

class ColorFormatter(logging.Formatter):
    def format(self, record):
        original_levelname = record.levelname
        original_pathname = record.pathname

        # Získáme cestu relativní vůči PROJECT_ROOT, handle cross-drive paths
        try:
            relative_path = os.path.relpath(original_pathname, PROJECT_ROOT)
        except ValueError:
            # If paths are on different drives, just use the filename
            relative_path = os.path.basename(original_pathname)

        if original_levelname == "ERROR":
            record.levelname = f"{RED}{original_levelname}{RESET}"
        elif original_levelname == "DEBUG":
            record.levelname = f"{BLUE}{original_levelname}{RESET}"
        elif original_levelname == "INFO":
            record.levelname = f"{GREEN}{original_levelname}{RESET}"
        elif original_levelname == "WARNING":
            record.levelname = f"{YELLOW}{original_levelname}{RESET}"
        else:
            record.levelname = f"{GRAY}{original_levelname}{RESET}"

        record.pathname = f"{GRAY}{relative_path}{RESET}"

        msg = super().format(record)

        record.levelname = original_levelname
        record.pathname = original_pathname
        return msg

# Logger settings
logger = logging.getLogger()
logger.setLevel(logging.WARNING)
logger.propagate = False

handler = logging.StreamHandler()
handler.setFormatter(ColorFormatter("%(levelname)s:%(pathname)s: %(message)s"))
logger.addHandler(handler)
