import ssl
import json
import paho.mqtt.client as mqtt

MQTT_HOST = "mqtt.3idlabs.eu"
MQTT_PORT = 8883
MQTT_USERNAME = "b-003"
MQTT_PASSWORD = "ECE27m8YMR"
MQTT_CLIENT_ID = "b-003-publisher"

TOPIC = "devices/b-003/commands/electronic/door_state"
PAYLOAD = {
    "request_uuid": "1231-24",
    "section_id": 2
}

def on_connect(client, userdata, flags, reason_code, properties=None):
    print(f"[CONNECTED] {reason_code}")
    if reason_code == 0:
        client.publish(TOPIC, json.dumps(PAYLOAD), qos=1, retain=True)
        print(f"[PUBLISHED] -> {TOPIC}: {PAYLOAD}")
        client.disconnect()

def on_disconnect(client, userdata, reason_code, properties=None):
    print(f"[DISCONNECTED] {reason_code}")

def on_log(client, userdata, level, buf):
    print(f"[LOG] {buf}")

client = mqtt.Client(client_id=MQTT_CLIENT_ID, protocol=mqtt.MQTTv5)
client.username_pw_set(MQTT_USERNAME, MQTT_PASSWORD)
client.tls_set(cert_reqs=ssl.CERT_REQUIRED)
client.tls_insecure_set(False)

client.on_connect = on_connect
client.on_disconnect = on_disconnect
client.on_log = on_log

print("[CONNECTING]")
client.connect(MQTT_HOST, MQTT_PORT, keepalive=60)
client.loop_forever()
