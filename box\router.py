from fastapi import APIRouter, HTTPException, Depends
from infrastructure.repositories.section_repository import section_repository
from fastapi.responses import JSONResponse, FileResponse, StreamingResponse
from pydantic import BaseModel
from auth.auth_bearer import <PERSON><PERSON><PERSON><PERSON>
from os import getenv, path
import os
import requests
import uuid
import json
import mysql.connector
from managers.timeline_logger import log_timeline_event
from managers.permissions import get_permissions_by_role
from infrastructure.external_apis.jetveo_client import jetveo_client

router = APIRouter()

# Initialize BearerAuth
auth_scheme = BearerAuth()

class PinCheckRequest(BaseModel):
    pin: str

class UnlockSectionRequest(BaseModel):
    section_id: int

# Load PINs from .env --> add to config.py !!!!!!!!!!!!!!
GS_MANAGER_PIN = getenv("GS_MANAGER_PIN")
GS_SERVICE_PIN = getenv("GS_SERVICE_PIN")
GS_INSTALLATION_PIN = getenv("GS_INSTALLATION_PIN")
CP_MANAGER_PIN = getenv("CP_MANAGER_PIN")
CP_SERVICE_PIN = getenv("CP_SERVICE_PIN")

# Load configuration for PIN check --> add to config.py !!!!!!!!!!!!!!
SERIAL_NUMBER = getenv("SERIAL_NUMBER")

# Load configuration for images  --> add to config.py !!!!!!!!!!!!!!
ASSETS_BASE_PATH = getenv("ASSETS_BASE_PATH", "assets")
BOX_BACKGROUND_IMAGE = getenv("BOX_BACKGROUND_IMAGE", "1.png")
BOX_HOMEPAGE_IMAGE = getenv("BOX_HOMEPAGE_IMAGE", "default.png")
BOX_LOGO_IMAGE = getenv("BOX_LOGO_IMAGE", "default.png")

# Load box configuration --> add to config.py !!!!!!!!!!!!!!
BOX_TYPE = getenv("BOX_TYPE", "sale_custom")
BOX_THEME = getenv("BOX_THEME", "brown")

# Load image usage flags --> add to config.py !!!!!!!!!!!!!!
BOX_BACKGROUND_USE_IMAGE = getenv("BOX_BACKGROUND_USE_IMAGE", "false").lower() == "true"
BOX_HOMEPAGE_USE_IMAGE = getenv("BOX_HOMEPAGE_USE_IMAGE", "false").lower() == "true"
BOX_LOGO_USE_IMAGE = getenv("BOX_LOGO_USE_IMAGE", "false").lower() == "true"

# Load feature flags --> add to config.py !!!!!!!!!!!!!!
FEATURE_STORAGE = getenv("FEATURE_STORAGE", "false").lower() == "true"
FEATURE_SALE = getenv("FEATURE_SALE", "false").lower() == "true"
FEATURE_ORDERS = getenv("FEATURE_ORDERS", "false").lower() == "true"
FEATURE_SENT = getenv("FEATURE_SENT", "false").lower() == "true"
FEATURE_EMPLOYES_DELIVERY = getenv("FEATURE_EMPLOYES_DELIVERY", "false").lower() == "true"


@router.get("/unlock")
async def unlock_section(request: UnlockSectionRequest):
    from hardware.locker_control import LockerController
    locker_controller = LockerController()
    success = await locker_controller.unlock_locker(request.section_id)
    return JSONResponse(content={"success": success})



@router.get("/sections")
def get_box_sections():
    sections = section_repository.list_sections()
    return JSONResponse(content=sections)

@router.get("/layout")
def get_box_layout(token: str = Depends(auth_scheme)):
    try:
        layout = section_repository.get_box_layout()

        if not layout:
            return JSONResponse(content={"error": "No layout found in database"}, status_code=404)
    except Exception as e:
        print(f"Error getting layout: {e}")
        return JSONResponse(content={"error": "Failed to load layout data"}, status_code=500)

    # Debug: print layout structure
    print(f"Layout structure: {list(layout.keys())}")
    if "columns" in layout:
        print(f"Number of columns: {len(layout['columns'])}")
        if len(layout['columns']) > 0:
            print(f"First column structure: {list(layout['columns'][0].keys())}")
            if "rows" in layout['columns'][0]:
                print(f"Number of rows in first column: {len(layout['columns'][0]['rows'])}")
                if len(layout['columns'][0]['rows']) > 0:
                    print(f"First row structure: {list(layout['columns'][0]['rows'][0].keys())}")

    for column in layout["columns"]:
        for row in column["rows"]:
            # Handle both "cells" and "sections" keys
            cells_key = None
            if "cells" in row:
                cells_key = "cells"
            elif "sections" in row:
                cells_key = "sections"
            else:
                print(f"Row structure: {list(row.keys())}")
                continue
            
            for cell in row[cells_key]:
                section_id = cell["section_id"]
                template_service = cell.get("service", 0)
                section_data = section_repository.get_section_data(section_id)

                cell["tempered"] = section_data["tempered"]
                cell["blocked"] = section_data["blocked"]

                if template_service == 1:
                    cell["service"] = 1
                    cell["visible"] = 1
                else:
                    cell["service"] = section_data["service"]
                    cell["visible"] = section_data["visible"]

    return JSONResponse(content=layout)

async def create_operator_session():
    """Helper function to create operator session"""
    from managers.session_manager import session_manager, SessionType, SessionStatus

    session_id = f"operator_{uuid.uuid4()}"  # Prefix for operator
    close = False  # Default value for operator sessions

    session = session_manager.create_session(
        session_id=session_id,
        session_type=SessionType.OPERATOR_FSM,
        status=SessionStatus.ACTIVE,
        expiry_minutes=15,
        session_data={"operator_close": close},
        endpoint_type="operator"
    )

    return {
        "session_id": session_id,
        "websocket_url": f"/ws/{session_id}",
        "message": "Operator session created",
        "expires_at": session.expires_at.isoformat() if session.expires_at else None,
        "close": close
    }

@router.post("/check-pin")
async def check_pin(request: PinCheckRequest):
    pin = request.pin.upper()  # Convert PIN to uppercase
    pin_length = len(pin)

    print(f"Scanned PIN: {request.pin} (normalized to: {pin})")


    # Check pins form .env file
    if pin_length == 10:
        # Log timeline event for 10-character PIN attempt
        log_timeline_event(
            event_type="operator_login",
            event_result="env_check_initiated",
            message="10-character PIN entered, checking against .env configuration",
            entered_pin=pin,
            mode="operator"
        )

        # Define PIN mappings for easier management
        pin_mappings = {
            GS_MANAGER_PIN.upper() if GS_MANAGER_PIN else None: ("manager", "GS Manager"),
            GS_SERVICE_PIN.upper() if GS_SERVICE_PIN else None: ("service", "GS Service"),
            GS_INSTALLATION_PIN.upper() if GS_INSTALLATION_PIN else None: ("montage", "GS Installation"),
            CP_MANAGER_PIN.upper() if CP_MANAGER_PIN else None: ("manager", "CP Manager"),
            CP_SERVICE_PIN.upper() if CP_SERVICE_PIN else None: ("service", "CP Service")
        }

        # Check if PIN matches any configured PIN
        for configured_pin, (operator_type, pin_description) in pin_mappings.items():
            if configured_pin and pin == configured_pin:
                # Create operator session for allowed PIN
                session_data = await create_operator_session()

                # Get permissions for the operator role
                permissions = get_permissions_by_role(operator_type)

                # Log successful PIN validation
                log_timeline_event(
                    event_type="operator_login",
                    event_result="allowed",
                    message=f"10-character PIN allowed - {pin_description}",
                    entered_pin=pin,
                    mode=operator_type,
                    session_id=session_data["session_id"],
                    operator_id="default"
                )

                # Build response with permissions
                response_data = {
                    "grand": "allow",
                    "type": operator_type,
                    **session_data
                }

                # Add permissions data if available
                if permissions:
                    # Extract data from permissions and add at same level
                    data = permissions.pop("data", {})
                    response_data["permissions"] = permissions
                    response_data["data"] = data

                return JSONResponse(content=response_data)

        # If no PIN matched, log denial and return deny response
        log_timeline_event(
            event_type="operator_login",
            event_result="denied",
            message="10-character PIN not found in .env configuration",
            entered_pin=pin,
            mode="unknown"
        )

        return JSONResponse(content={
            "grand": "deny",
            "type": "unknown"
        })

    # Check operator pin from server
    elif pin_length == 9:
        try:
            # Use jetveo_client to check operator PIN
            response_data = await jetveo_client.check_operator_pin(pin)

            # Handle response data
            if response_data and response_data.get("status") == "allow":
                    # Create operator session for allowed PIN
                    session_data = await create_operator_session()

                    # Determine operator type based on response
                    match response_data.get("type"):
                        case "0": operator_type = "courier"
                        case "1": operator_type = "hygiene"
                        case "2": operator_type = "manager"
                        case "3": operator_type = "service"
                        case _:   operator_type = "unknown"

                    # Get permissions for the operator role
                    permissions = get_permissions_by_role(operator_type)

                    log_timeline_event(
                        event_type="operator_login",
                        event_result="allowed",
                        message=f"9-character PIN allowed by server - {response_data.get('name', 'Unknown')} ({operator_type})",
                        entered_pin=pin,
                        operator_id=response_data.get("operator_id"),
                        mode=operator_type,
                        session_id=session_data["session_id"]
                    )

                    # Build response with permissions
                    response_content = {
                        "grand": "allow",
                        "type": operator_type,
                        "operator_id": response_data.get("operator_id"),
                        "name": response_data.get("name"),
                        **session_data
                    }

                    # Add permissions data if available
                    if permissions:
                        # Extract data from permissions and add at same level
                        data = permissions.pop("data", {})
                        response_content["permissions"] = permissions
                        response_content["data"] = data

                    return JSONResponse(content=response_content)
            else:
                # PIN denied or no response data
                if response_data:
                    log_timeline_event(
                        event_type="operator_login",
                        event_result="denied",
                        message="9-character PIN denied by server",
                        entered_pin=pin,
                        mode="unknown"
                    )
                    return JSONResponse(content={
                        "grand": "deny",
                        "type": "server_denied"
                    })
                else:
                    log_timeline_event(
                        event_type="operator_login",
                        event_result="failed_to_check",
                        message="No response data from server",
                        entered_pin=pin,
                        mode="unknown"
                    )
                    return JSONResponse(content={
                        "grand": "deny",
                        "type": "server_error"
                    })

        except requests.exceptions.Timeout:
            log_timeline_event(
                event_type="operator_login",
                event_result="failed_to_check",
                message="Server request timed out",
                entered_pin=pin,
                mode="unknown"
            )
            return JSONResponse(content={
                "grand": "deny",
                "type": "server_timeout"
            })
        except requests.exceptions.RequestException as e:
            log_timeline_event(
                event_type="operator_login",
                event_result="failed_to_check",
                message=f"Server request failed: {str(e)}",
                entered_pin=pin,
                mode="unknown"
            )
            return JSONResponse(content={
                "grand": "deny",
                "type": "server_error"
            })
        except Exception as e:
            log_timeline_event(
                event_type="operator_login",
                event_result="failed_to_check",
                message=f"Unexpected error during server check: {str(e)}",
                entered_pin=pin,
                mode="unknown"
            )
            return JSONResponse(content={
                "grand": "deny",
                "type": "internal_error"
            })

    else:
        return JSONResponse(content={
            "grand": "deny",
            "type": "invalid_length"
        })



@router.get("/background")
def get_background_image():
    requested_image_path = path.join(ASSETS_BASE_PATH, "background", BOX_BACKGROUND_IMAGE)
    default_image_path = path.join(ASSETS_BASE_PATH, "background", "default.png")
    if BOX_BACKGROUND_IMAGE.startswith(('http://', 'https://')):
        try:
            response = requests.get(BOX_BACKGROUND_IMAGE, timeout=10)
            response.raise_for_status()

            content_type = response.headers.get('content-type', 'image/png')

            return StreamingResponse(
                iter([response.content]),
                media_type=content_type
            )
        except requests.RequestException:
            if path.exists(default_image_path):
                return FileResponse(default_image_path)
            else:
                raise HTTPException(status_code=404, detail="Background image not found")

    if path.exists(requested_image_path):
        return FileResponse(requested_image_path)

    if path.exists(default_image_path):
        return FileResponse(default_image_path)

    raise HTTPException(status_code=404, detail="Background image not found")


@router.get("/homepage")
def get_homepage_image():
    # Use default.png if BOX_HOMEPAGE_IMAGE is empty or not defined
    homepage_image = BOX_HOMEPAGE_IMAGE if BOX_HOMEPAGE_IMAGE else "default.png"
    requested_image_path = path.join(ASSETS_BASE_PATH, "homepage", homepage_image)
    default_image_path = path.join(ASSETS_BASE_PATH, "homepage", "default.png")
    if homepage_image.startswith(('http://', 'https://')):
        try:
            response = requests.get(homepage_image, timeout=10)
            response.raise_for_status()

            content_type = response.headers.get('content-type', 'image/png')

            return StreamingResponse(
                iter([response.content]),
                media_type=content_type
            )
        except requests.RequestException:
            if path.exists(default_image_path):
                return FileResponse(default_image_path)
            else:
                raise HTTPException(status_code=404, detail="Homepage image not found")

    if path.exists(requested_image_path):
        return FileResponse(requested_image_path)

    if path.exists(default_image_path):
        return FileResponse(default_image_path)

    raise HTTPException(status_code=404, detail="Homepage image not found")


@router.get("/logo")
def get_logo_image():
    # Use default.png if BOX_LOGO_IMAGE is empty or not defined
    logo_image = BOX_LOGO_IMAGE if BOX_LOGO_IMAGE else "default.png"
    requested_image_path = path.join(ASSETS_BASE_PATH, "logo", logo_image)
    default_image_path = path.join(ASSETS_BASE_PATH, "logo", "default.png")
    if logo_image.startswith(('http://', 'https://')):
        try:
            response = requests.get(logo_image, timeout=10)
            response.raise_for_status()

            content_type = response.headers.get('content-type', 'image/png')

            return StreamingResponse(
                iter([response.content]),
                media_type=content_type
            )
        except requests.RequestException:
            if path.exists(default_image_path):
                return FileResponse(default_image_path)
            else:
                raise HTTPException(status_code=404, detail="Logo image not found")

    if path.exists(requested_image_path):
        return FileResponse(requested_image_path)

    if path.exists(default_image_path):
        return FileResponse(default_image_path)

    raise HTTPException(status_code=404, detail="Logo image not found")


@router.get("/settings")
def get_box_settings(token: str = Depends(auth_scheme)):
    """
    Get box settings based on BOX_TYPE configuration.
    Returns different settings structure based on box type.
    """
    settings = {
        "type": BOX_TYPE
    }
    
    if BOX_TYPE == "sale_custom":
        settings.update({
            "purchase": FEATURE_SALE,
            "reservation": True
        })
    elif BOX_TYPE == "order":
        settings.update({
            "pickup": FEATURE_ORDERS,
            "send": FEATURE_SENT,
            "delivery": FEATURE_EMPLOYES_DELIVERY
        })

    elif BOX_TYPE == "storage":
        settings.update({
            "pickup": FEATURE_STORAGE,
            "store": FEATURE_STORAGE
        })
    
    # Create theme configuration separately
    theme = {
        "color_palette": BOX_THEME,
        "background_image": BOX_BACKGROUND_USE_IMAGE,
        "homepage_image": BOX_HOMEPAGE_USE_IMAGE,
        "logo_image": BOX_LOGO_USE_IMAGE
    }
    
    return JSONResponse(content={
        "settings": settings,
        "theme": theme
    })


def get_database_connection():
    """Get database connection using environment variables"""
    return mysql.connector.connect(
        host=getenv("DB_HOST"),
        port=int(getenv("DB_PORT", "3306")),
        database=getenv("DB_NAME"),
        user=getenv("DB_USER"),
        password=getenv("DB_PASSWORD")
    )


@router.post("/provision/clean")
async def clean_layout():
    """
    Clean layout by deleting all records from box_settings and box_sections tables.
    """
    try:
        conn = get_database_connection()
        cursor = conn.cursor()

        # Delete all records from box_sections table
        cursor.execute("DELETE FROM box_sections")
        sections_deleted = cursor.rowcount

        # Delete all records from box_settings table
        cursor.execute("DELETE FROM box_settings")
        settings_deleted = cursor.rowcount

        conn.commit()

        log_timeline_event(
            event_type="layout_cleaned",
            event_result="success",
            message=f"Layout cleaned successfully. Deleted {sections_deleted} sections and {settings_deleted} settings records.",
            mode="box"
        )

        return JSONResponse(content={
            "success": True,
            "message": "Layout cleaned successfully"
        })

    except Exception as e:
        if 'conn' in locals():
            conn.rollback()

        log_timeline_event(
            event_type="layout_cleaned",
            event_result="failed",
            message=f"Failed to clean layout: {str(e)}",
            mode="box"
        )

        return JSONResponse(content={
            "success": False,
            "message": f"Failed to clean layout: {str(e)}"
        })

    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()


@router.post("/provision/layout")
async def update_layout():
    """
    Update layout by fetching data from external API and saving to box_settings and box_sections tables.
    """
    try:
        # Get serial number from environment
        serial_number = getenv("SERIAL_NUMBER")
        if not serial_number:
            return JSONResponse(content={
                "success": False,
                "message": "SERIAL_NUMBER not configured in environment"
            })

        # Use jetveo_client to fetch layout data
        api_data = await jetveo_client.get_layout(serial_number)

        if not api_data:
            log_timeline_event(
                event_type="layout_update",
                event_result="failed",
                message="Failed to fetch layout data from external API",
                mode="box"
            )
            return JSONResponse(content={
                "success": False,
                "message": "Failed to fetch layout data from external API"
            })


        # Extract data from API response
        result = api_data.get("result", {})
        layout_id = result.get("layout_id")
        layout_name = result.get("layout_name")
        layout_data = result.get("layout_data")

        if not layout_id or not layout_name or not layout_data:
            return JSONResponse(content={
                "success": False,
                "message": "Invalid response from external API - missing required fields"
            })

        # Start database transaction
        conn = get_database_connection()
        cursor = conn.cursor()

        try:
            # Save to box_settings table
            box_settings_query = """
                INSERT INTO box_settings (box_uuid, box_layout_id, box_layout, created_at, changed_at)
                VALUES (%s, %s, %s, NOW(), NOW())
            """
            cursor.execute(box_settings_query, (layout_id, layout_name, json.dumps(layout_data)))

            # Process sections from layout data and save to box_sections table
            sections_inserted = 0
            for column in layout_data.get("columns", []):
                for row in column.get("rows", []):
                    for section in row.get("sections", []):
                        section_uuid = str(uuid.uuid4())
                        section_type = section.get("section_type", 0)

                        # Map section_type to mode
                        match section_type:
                            case 0: mode = "storage"
                            case 1: mode = "order"
                            case 2: mode = "sale"
                            case _: mode = "storage"

                        box_sections_query = """
                            INSERT INTO box_sections (
                                uuid, box_uuid, section_id, identification_name, tempered, visible, blocked, service,
                                lock_id, led_section, fixed_pin, pin, size_category, size_width, size_depth, size_height, mode, type
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """

                        cursor.execute(box_sections_query, (
                            section_uuid,                           # uuid
                            layout_id,                              # box_uuid
                            section.get("section_id"),              # section_id
                            section.get("section_id"),              # identification_name
                            1 if section.get("tempered", False) else 0,  # tempered
                            1 if section.get("visible", True) else 0,    # visible
                            1 if section.get("blocked", False) else 0,   # blocked
                            1 if section.get("service", False) else 0,   # service
                            section.get("lock_id"),                 # lock_id
                            section.get("led_id"),                  # led_section
                            0,                                      # fixed_pin
                            None,                                   # pin
                            section.get("size_category"),          # size_category
                            section.get("size_width"),             # size_width
                            section.get("size_depth"),             # size_depth
                            section.get("size_height"),            # size_height
                            mode,                                   # mode
                            ""                                      # type (empty string as per migration default)
                        ))
                        sections_inserted += 1

            conn.commit()

            log_timeline_event(
                event_type="layout_update",
                event_result="success",
                message=f"Layout updated successfully. Inserted {sections_inserted} sections.",
                mode="box"
            )

            return JSONResponse(content={
                "success": True,
                "message": "Layout updated successfully"
            })

        except Exception as db_error:
            conn.rollback()
            raise db_error

    except Exception as e:
        log_timeline_event(
            event_type="layout_update",
            event_result="failed",
            message=f"Failed to update layout: {str(e)}",
            mode="box"
        )
        return JSONResponse(content={
            "success": False,
            "message": f"Failed to update layout: {str(e)}"
        })

    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()