-- migrate:up
CREATE TABLE order_reservations (
    id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    order_uuid CHAR(36) NOT NULL DEFAULT (UUID()),
    order_number VARCHAR(255) DEFAULT NULL,
    -- serial_number VARCHAR(255) DEFAULT NULL,         -- tohle asi bude serial number boxu?
    status INT(4) NOT NULL DEFAULT 0,
    section_id INT(11) DEFAULT NULL,
    insert_pin VARCHAR(255) DEFAULT NULL,
    package_pin VARCHAR(255) DEFAULT NULL,
    pickup_pin VARCHAR(255) DEFAULT NULL,
    card_number VARCHAR(255) DEFAULT NULL,
    age_control_required TINYINT(2) DEFAULT 0,
    age_controlled TINYINT(2) NOT NULL DEFAULT 0,
    phone_number VARCHAR(255) DEFAULT NULL,
    max_days INT(11) DEFAULT NULL,
    price DOUBLE NULL DEFAULT 0,
    payment_required TINYINT(2) DEFAULT 0,
    paid_status TINYINT(2) DEFAULT 0,
    expired TINYINT(2) NOT NULL DEFAULT 0,
    type VA<PERSON>HAR(255) DEFAULT NULL,

    -- for ourier to insert
    size_category INT(11) DEFAULT NULL,             -- size category of the reservation
    tempered TINYINT(2) NULL DEFAULT 0,             -- if reservation require tempered section
    sectionc_can_change TINYINT(2) NULL DEFAULT 0,  -- if courier can change section

    last_update TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;



-- migrate:down
DROP TABLE order_reservations;












-- old version
-- -- migrate:up
-- CREATE TABLE order_reservations (
--     id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
--     uuid CHAR(36) NOT NULL DEFAULT (UUID()),
--     order_number VARCHAR(255) DEFAULT NULL,
--     box_uuid VARCHAR(255) DEFAULT NULL,
--     section_id VARCHAR(255) DEFAULT NULL,
--     status INT(11) NOT NULL DEFAULT 0,
--     insert_pin VARCHAR(255) DEFAULT NULL,
--     pickup_pin VARCHAR(255) DEFAULT NULL,
--     phone_number VARCHAR(255) DEFAULT NULL,
--     size_category INT(11) DEFAULT NULL,
--     expired INT(11) DEFAULT NULL,
--     type VARCHAR(255) DEFAULT NULL,
--     last_update TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP(),
--     created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP()
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- -- migrate:down
-- DROP TABLE order_reservations;
